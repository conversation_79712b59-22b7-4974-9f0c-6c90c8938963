# 任务单

# 需求

请对 `c:\Users\<USER>\source\BaiYunGroup/webgl/touchmain.html` 文件进行以下具体修改，以实现基于iframe的页面导航系统和性能优化：

## 1. 添加iframe容器
- 在 `main-content` 区域内添加一个全覆盖的iframe元素
- iframe应具有以下属性：
  - 位置：覆盖在 `left-panel` 和 `center-panel` 之上
  - 尺寸：填满整个 `main-content` 区域
  - 初始状态：隐藏（`display: none`）
  - z-index：确保在其他内容之上
  - 边框：无边框样式以保持界面一致性

## 2. 添加主页面导航按钮
- 在现有的 `nav-item` 导航区域最前面（第一个位置）添加"主页面"按钮
- 按钮样式应与现有导航按钮保持一致
- 点击"主页面"按钮的功能：
  - 隐藏iframe容器（`display: none`）
  - 显示原始的 `left-panel` 和 `center-panel` 内容
  - 更新按钮的激活状态样式

## 3. 配置子页面导航逻辑
- 识别现有 `nav-item` 中的所有导航按钮，排除"权限"和新添加的"主页面"按钮
- 为每个子页面导航按钮添加点击事件处理：
  - 显示iframe容器（`display: block`）
  - 在iframe中加载对应的页面URL
  - 更新当前激活按钮的样式状态

## 4. 实现页面预加载和缓存机制
- 在页面初始化时创建隐藏的预加载iframe元素
- 预加载策略：
  - 页面加载完成后，依次预加载所有子页面
  - 使用JavaScript创建隐藏的iframe元素进行预加载
  - 实现页面缓存映射表，记录已加载的页面
- 切换优化：
  - 用户点击导航时，优先使用已缓存的页面内容
  - 如果页面未缓存，则显示加载状态并动态加载
  - 实现平滑的页面切换动画效果

## 5. 样式和兼容性要求
- 保持现有的深色科技主题和工业监控界面风格
- 确保iframe内容与主界面样式协调
- 维持与Unity WebGL项目的兼容性
- 响应式设计适配不同屏幕尺寸




## 6. 技术实现细节
- 使用现有的CSS类名和样式规范
- JavaScript代码应遵循现有的代码结构和命名约定
- 确保不影响现有的Unity WebGL功能
- 添加错误处理机制，处理页面加载失败的情况

请在修改前先查看当前的 `touchmain.html` 文件结构，了解现有的导航按钮、样式类名和JavaScript函数，然后基于现有代码进行增量修改。