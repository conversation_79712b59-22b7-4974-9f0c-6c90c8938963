# 任务单

# 需求

请对 `c:\Users\<USER>\source\BaiYunGroup/webgl/touchmain.html` 文件进行以下具体修改，以实现基于iframe的页面导航系统和性能优化：

## 1. 添加iframe容器
- 在 `main-content` 区域内添加一个全覆盖的iframe元素
- iframe应具有以下属性：
  - 位置：覆盖在 `left-panel` 和 `center-panel` 之上
  - 尺寸：填满整个 `main-content` 区域
  - 初始状态：隐藏（`display: none`）
  - z-index：确保在其他内容之上
  - 边框：无边框样式以保持界面一致性

## 2. 添加主页面导航按钮
- 在现有的 `nav-item` 导航区域最前面（第一个位置）添加"主页面"按钮
- 按钮样式应与现有导航按钮保持一致
- 点击"主页面"按钮的功能：
  - 隐藏iframe容器（`display: none`）
  - 显示原始的 `left-panel` 和 `center-panel` 内容
  - 更新按钮的激活状态样式

## 3. 配置子页面导航逻辑
- 识别现有 `nav-item` 中的所有导航按钮，排除"权限"和新添加的"主页面"按钮
- 为每个子页面导航按钮添加点击事件处理：
  - 显示iframe容器（`display: block`）
  - 在iframe中加载对应的页面URL
  - 更新当前激活按钮的样式状态

## 4. 实现页面预加载和缓存机制
- 在页面初始化时创建隐藏的预加载iframe元素
- 预加载策略：
  - 页面加载完成后，依次预加载所有子页面
  - 使用JavaScript创建隐藏的iframe元素进行预加载
  - 实现页面缓存映射表，记录已加载的页面
- 切换优化：
  - 用户点击导航时，优先使用已缓存的页面内容
  - 如果页面未缓存，则显示加载状态并动态加载
  - 实现平滑的页面切换动画效果

## 5. 样式和兼容性要求
- 保持现有的深色科技主题和工业监控界面风格
- 确保iframe内容与主界面样式协调
- 维持与Unity WebGL项目的兼容性
- 响应式设计适配不同屏幕尺寸

## 6. 技术实现细节
- 使用现有的CSS类名和样式规范
- JavaScript代码应遵循现有的代码结构和命名约定
- 确保不影响现有的Unity WebGL功能
- 添加错误处理机制，处理页面加载失败的情况



# 任务拆解
请先查看当前的 `touchmain.html` 文件结构，了解现有的导航按钮、样式类名和JavaScript函数，把需求细分拆解为不同的子任务，确保每个任务有明确的完成要求，便于研发实现、测试和review。

# 精修：
1. iframe-navigation-container的高度和main-content的高度保持一致
2. 调试参数 和 调试参数2 两个菜单对应的页面也需要放到  iframe-navigation-container 里面，包括debugMenuButton 的逻辑。
## 3. 页面加载时把所有页面都预加载到iframe-navigation-container里面，这样用户点击菜单时就不需要加载了。


实现iframe导航系统的全页面预加载优化功能，在页面初始化时将所有可访问的模块页面预加载到隐藏的iframe容器中，实现用户点击导航菜单时的即时切换体验。

**具体实现要求：**

1. **文件分析阶段**：
   - 查看当前 `touchmain.html` 文件中的iframe-navigation-container结构
   - 识别现有的导航按钮（nav-item元素）及其对应的模块配置
   - 分析当前的页面缓存机制（pageCache对象）和预加载逻辑
   - 了解现有的权限控制逻辑，确定哪些页面需要预加载

2. **任务分解要求**：
   将需求拆分为以下具体子任务，每个任务需包含：
   - 任务名称和详细描述
   - 具体的技术实现方案（HTML结构修改、JavaScript函数实现、CSS样式调整）
   - 明确的完成标准和验收条件
   - 性能优化考虑（避免同时加载过多页面影响性能）
   - 错误处理机制（页面加载失败的处理）
   - 与现有功能的兼容性保证

3. **预加载策略**：
   - 创建隐藏的iframe容器池，为每个可访问模块创建独立的iframe
   - 实现智能预加载顺序（优先加载常用页面，延迟加载权限受限页面）
   - 建立页面状态管理机制（已加载、加载中、加载失败、未加载）
   - 实现页面切换时的即时显示逻辑（从预加载池中切换显示的iframe）

4. **性能和用户体验优化**：
   - 避免页面初始化时的性能影响（分批预加载、延迟加载）
   - 提供加载进度指示（可选）
   - 实现内存管理机制（清理不常用的预加载页面）
   - 确保预加载不影响主要功能的正常使用

5. **兼容性和测试要求**：
   - 确保与现有的权限控制、登录状态检查完全兼容
   - 保持调试功能、模块弹窗等现有功能正常工作
   - 提供降级方案（预加载失败时回退到原有的按需加载模式）
   - 包含完整的测试验证步骤

请基于以上要求进行详细的任务分解，确保每个子任务都有明确的技术实现路径和验收标准。